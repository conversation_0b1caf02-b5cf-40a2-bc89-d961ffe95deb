# DSA 项目文档

本目录包含DSA项目的详细文档，包括API使用指南、设计文档和性能分析。

## 📚 文档目录

### API 使用指南
- [数组列表 API](array_list_api.md) - 静态数组和动态数组的完整API文档
- [链表 API](linked_list_api.md) - 单链表、双链表和循环链表的API文档
- [栈 API](stack_api.md) - 数组栈和链表栈的API文档
- [双端队列 API](deque_api.md) - 循环数组和双向链表双端队列的API文档
- [队列 API](queue_api.md) - 循环数组和双向链表队列的API文档

### 设计文档
- [架构设计](architecture.md) - 项目整体架构和设计原则
- [性能分析](performance.md) - 各种数据结构的性能对比和分析
- [内存管理](memory_management.md) - 内存分配和释放策略

### 开发指南
- [构建指南](build_guide.md) - 如何构建和测试项目
- [贡献指南](contributing.md) - 如何为项目贡献代码
- [代码规范](coding_standards.md) - 代码风格和规范要求

## 🚀 快速开始

如果你是第一次使用DSA库，建议按以下顺序阅读文档：

1. 先阅读主项目的 [README.md](../README.md) 了解项目概况
2. 查看 [构建指南](build_guide.md) 学习如何编译和运行
3. 根据需要选择相应的API文档学习具体用法
4. 参考 [性能分析](performance.md) 选择最适合的数据结构

## 📝 文档维护

文档与代码同步更新，如果发现文档与实际代码不符，请提交issue或PR。

---

返回 [主项目](../README.md)
