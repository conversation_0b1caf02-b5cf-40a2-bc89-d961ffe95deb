@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# 检查所需的组件
check_required_components(@PROJECT_NAME@)

# 包含导出的目标
if(NOT TARGET @PROJECT_NAME@::@PROJECT_NAME@)
    include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
endif()

# 设置变量
set(@PROJECT_NAME@_FOUND TRUE)
set(@PROJECT_NAME@_VERSION @PROJECT_VERSION@)
set(@PROJECT_NAME@_VERSION_MAJOR @PROJECT_VERSION_MAJOR@)
set(@PROJECT_NAME@_VERSION_MINOR @PROJECT_VERSION_MINOR@)
set(@PROJECT_NAME@_VERSION_PATCH @PROJECT_VERSION_PATCH@)

# 提供库目标
set(@PROJECT_NAME@_LIBRARIES @PROJECT_NAME@::@PROJECT_NAME@)
set(@PROJECT_NAME@_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include")